const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// 读取用户脚本内容
const userScriptPath = path.join(__dirname, '..', '80.lv Articles Page Navigation (v2.8 Enhanced Navigation)-2.8.user.js');
const userScriptContent = fs.readFileSync(userScriptPath, 'utf8');

test.describe('80.lv Articles Navigation Enhancement', () => {
  test.beforeEach(async ({ page }) => {
    // 注入用户脚本
    await page.addInitScript(userScriptContent);

    // 导航到80.lv articles页面
    await page.goto('https://80.lv/articles');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
  });

  test('应该显示导航UI控件', async ({ page }) => {
    // 等待导航UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    // 验证UI元素存在
    const container = page.locator('#tm-page-nav-container-80lv-v28');
    await expect(container).toBeVisible();

    const input = page.locator('#tm-page-input-80lv-v28');
    await expect(input).toBeVisible();

    const prevButton = page.locator('#tm-page-prev-button-80lv-v28');
    await expect(prevButton).toBeVisible();

    const nextButton = page.locator('#tm-page-next-button-80lv-v28');
    await expect(nextButton).toBeVisible();

    const goButton = page.locator('#tm-page-go-button-80lv-v28');
    await expect(goButton).toBeVisible();

    console.log('✅ 导航UI控件已正确显示');
  });

  test('应该正确检测当前页面', async ({ page }) => {
    // 等待脚本初始化
    await page.waitForTimeout(2000);

    // 检查当前页面检测
    const currentPage = await page.evaluate(() => {
      return window.getCurrentPage ? window.getCurrentPage() : 1;
    });

    console.log(`✅ 当前页面检测: ${currentPage}`);
    expect(currentPage).toBeGreaterThanOrEqual(1);
  });

  test('应该能够使用前进/后退按钮', async ({ page }) => {
    // 等待导航UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    // 点击下一页按钮
    const nextButton = page.locator('#tm-page-next-button-80lv-v28');
    await nextButton.click();

    // 等待页面导航
    await page.waitForTimeout(3000);

    // 验证页面已更改
    const url = page.url();
    console.log(`✅ 点击下一页后的URL: ${url}`);

    // 点击上一页按钮
    const prevButton = page.locator('#tm-page-prev-button-80lv-v28');
    await prevButton.click();

    // 等待页面导航
    await page.waitForTimeout(3000);

    console.log('✅ 前进/后退按钮功能正常');
  });

  test('应该能够使用跳转功能', async ({ page }) => {
    // 等待导航UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    // 输入目标页面
    const input = page.locator('#tm-page-input-80lv-v28');
    await input.fill('3');

    // 点击跳转按钮
    const goButton = page.locator('#tm-page-go-button-80lv-v28');
    await goButton.click();

    // 等待导航完成
    await page.waitForTimeout(5000);

    console.log('✅ 跳转功能测试完成');
  });

  test('应该响应键盘快捷键', async ({ page }) => {
    // 等待导航UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    // 测试Alt+G快捷键
    await page.keyboard.press('Alt+g');

    // 验证输入框获得焦点
    const input = page.locator('#tm-page-input-80lv-v28');
    await expect(input).toBeFocused();

    console.log('✅ 键盘快捷键功能正常');
  });

  test('应该正确检测分页信息', async ({ page }) => {
    // 等待导航UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    // 检查分页信息显示
    const info = page.locator('#tm-page-info-80lv-v28');
    await expect(info).toBeVisible();

    const infoText = await info.textContent();
    console.log(`✅ 分页信息: ${infoText}`);

    expect(infoText).toContain('可见');
  });

  test('应该能够处理页面边界情况', async ({ page }) => {
    // 等待导航UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    // 在第一页时，上一页按钮应该被禁用
    const prevButton = page.locator('#tm-page-prev-button-80lv-v28');

    // 检查按钮状态
    const isDisabled = await prevButton.isDisabled();
    console.log(`✅ 第一页时上一页按钮禁用状态: ${isDisabled}`);
  });

  test('应该能够隐藏和显示UI', async ({ page }) => {
    // 等待导航UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    // 点击关闭按钮
    const closeButton = page.locator('#tm-page-close-button-80lv-v28');
    await closeButton.click();

    // 验证UI被隐藏
    const container = page.locator('#tm-page-nav-container-80lv-v28');
    await expect(container).toHaveClass(/hidden/);

    // 使用快捷键重新显示
    await page.keyboard.press('Alt+g');

    // 验证UI重新显示
    await expect(container).not.toHaveClass(/hidden/);

    console.log('✅ UI隐藏/显示功能正常');
  });
});

// 运行性能测试
test.describe('性能测试', () => {
  test('脚本加载性能', async ({ page }) => {
    const startTime = Date.now();

    // 注入用户脚本
    await page.addInitScript(userScriptContent);
    await page.goto('https://80.lv/articles');

    // 等待UI出现
    await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });

    const endTime = Date.now();
    const loadTime = endTime - startTime;

    console.log(`✅ 脚本加载时间: ${loadTime}ms`);
    expect(loadTime).toBeLessThan(10000); // 应该在10秒内加载完成
  });
});
